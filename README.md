# WordPress + WooCommerce Docker Setup

This setup provides a complete WordPress installation with WooCommerce ecommerce plugin using Docker.

## What's Included

- **WordPress**: Latest version
- **MySQL 8.0**: Database server
- **phpMyAdmin**: Database management interface
- **WooCommerce**: Ecommerce plugin (to be installed)

## Quick Start

1. **Start the containers:**
   ```bash
   docker-compose up -d
   ```

2. **Wait for services to start** (about 1-2 minutes)

3. **Access WordPress:**
   - Website: http://localhost:8080
   - Admin: http://localhost:8080/wp-admin

4. **Complete WordPress setup:**
   - Choose language
   - Create admin user
   - Set site title and description

5. **Install WooCommerce:**
   ```powershell
   # Windows PowerShell
   .\setup-woocommerce.ps1

   # Or use the management script
   .\manage-wordpress.ps1 install-woocommerce

   # Or manually install via WordPress admin panel
   ```

## Access Points

- **WordPress Site**: http://localhost:8080
- **WordPress Admin**: http://localhost:8080/wp-admin
- **phpMyAdmin**: http://localhost:8081

## Database Credentials

- **Database**: wordpress
- **Username**: wordpress
- **Password**: wordpress_password
- **Root Password**: root_password

## Useful Commands

### Using the Management Script (Recommended)
```powershell
# Start services
.\manage-wordpress.ps1 start

# Stop services
.\manage-wordpress.ps1 stop

# Restart services
.\manage-wordpress.ps1 restart

# Check status
.\manage-wordpress.ps1 status

# View logs
.\manage-wordpress.ps1 logs

# Install WooCommerce
.\manage-wordpress.ps1 install-woocommerce
```

### Direct Docker Commands
```bash
# Start services
docker-compose up -d

# Stop services
docker-compose down

# View logs
docker-compose logs -f

# Access WordPress container
docker exec -it wordpress_site bash

# Access database container
docker exec -it wordpress_db mysql -u wordpress -p

# Backup database
docker exec wordpress_db mysqldump -u wordpress -pwordpress_password wordpress > backup.sql

# Restore database
docker exec -i wordpress_db mysql -u wordpress -pwordpress_password wordpress < backup.sql
```

## File Structure

```
.
├── docker-compose.yml      # Docker services configuration
├── setup-woocommerce.ps1  # WooCommerce installation script (PowerShell)
├── setup-woocommerce.sh   # WooCommerce installation script (Bash)
├── manage-wordpress.ps1   # WordPress management script (PowerShell)
├── wp-content/            # WordPress content (themes, plugins, uploads)
└── README.md              # This file
```

## Customization

- **Themes and Plugins**: Place in `wp-content/themes/` and `wp-content/plugins/`
- **Uploads**: Stored in `wp-content/uploads/`
- **Configuration**: Modify `docker-compose.yml` for custom settings

## Troubleshooting

- If containers don't start, check if ports 8080 and 8081 are available
- For permission issues, ensure Docker has access to the project directory
- Database connection issues: wait longer for MySQL to initialize (first run takes time)
