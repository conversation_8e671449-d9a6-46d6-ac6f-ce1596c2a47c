#!/bin/bash

# <PERSON>ript to install WooCommerce plugin in Word<PERSON>ress
echo "Installing WooCommerce plugin..."

# Wait for WordPress to be ready
echo "Waiting for WordPress to be ready..."
sleep 30

# Download and install WooCommerce
docker exec wordpress_site wp plugin install woocommerce --activate --allow-root

# Install some useful WooCommerce extensions
echo "Installing additional WooCommerce plugins..."
docker exec wordpress_site wp plugin install woocommerce-gateway-stripe --allow-root
docker exec wordpress_site wp plugin install woocommerce-services --allow-root

# Set up basic WooCommerce settings
echo "Setting up basic WooCommerce configuration..."
docker exec wordpress_site wp option update woocommerce_store_address "123 Main Street" --allow-root
docker exec wordpress_site wp option update woocommerce_store_city "Your City" --allow-root
docker exec wordpress_site wp option update woocommerce_default_country "US:CA" --allow-root
docker exec wordpress_site wp option update woocommerce_store_postcode "12345" --allow-root
docker exec wordpress_site wp option update woocommerce_currency "USD" --allow-root

echo "WooCommerce setup complete!"
echo "You can now access your WordPress site at http://localhost:8080"
echo "Admin panel: http://localhost:8080/wp-admin"
echo "Database management: http://localhost:8081 (phpMyAdmin)"
