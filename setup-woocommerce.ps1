# PowerShell script to install WooCommerce plugin in WordPress
Write-Host "Installing WooCommerce plugin..." -ForegroundColor Green

# Wait for WordPress to be ready
Write-Host "Waiting for WordPress to be ready..." -ForegroundColor Yellow
Start-Sleep 30

# Install WP-CLI in the WordPress container first
Write-Host "Installing WP-CLI..." -ForegroundColor Yellow
docker exec wordpress_site bash -c "curl -O https://raw.githubusercontent.com/wp-cli/wp-cli/v2.8.1/phar/wp-cli.phar && php wp-cli.phar --info && chmod +x wp-cli.phar && mv wp-cli.phar /usr/local/bin/wp"

# Download and install WooCommerce
Write-Host "Installing WooCommerce plugin..." -ForegroundColor Yellow
docker exec wordpress_site wp plugin install woocommerce --activate --allow-root --path=/var/www/html

# Install some useful WooCommerce extensions
Write-Host "Installing additional WooCommerce plugins..." -ForegroundColor Yellow
docker exec wordpress_site wp plugin install woocommerce-gateway-stripe --allow-root --path=/var/www/html
docker exec wordpress_site wp plugin install woocommerce-services --allow-root --path=/var/www/html

# Set up basic WooCommerce settings
Write-Host "Setting up basic WooCommerce configuration..." -ForegroundColor Yellow
docker exec wordpress_site wp option update woocommerce_store_address "123 Main Street" --allow-root --path=/var/www/html
docker exec wordpress_site wp option update woocommerce_store_city "Your City" --allow-root --path=/var/www/html
docker exec wordpress_site wp option update woocommerce_default_country "US:CA" --allow-root --path=/var/www/html
docker exec wordpress_site wp option update woocommerce_store_postcode "12345" --allow-root --path=/var/www/html
docker exec wordpress_site wp option update woocommerce_currency "USD" --allow-root --path=/var/www/html

Write-Host "WooCommerce setup complete!" -ForegroundColor Green
Write-Host "You can now access your WordPress site at http://localhost:8080" -ForegroundColor Cyan
Write-Host "Admin panel: http://localhost:8080/wp-admin" -ForegroundColor Cyan
Write-Host "Database management: http://localhost:8081 (phpMyAdmin)" -ForegroundColor Cyan
