# WordPress Docker Management Script
param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("start", "stop", "restart", "status", "logs", "install-woocommerce")]
    [string]$Action
)

function Show-Status {
    Write-Host "WordPress Docker Status:" -ForegroundColor Cyan
    docker-compose ps
}

function Start-WordPress {
    Write-Host "Starting WordPress containers..." -ForegroundColor Green
    docker-compose up -d
    Write-Host "Containers started! WordPress will be available at http://localhost:8080" -ForegroundColor Green
}

function Stop-WordPress {
    Write-Host "Stopping WordPress containers..." -ForegroundColor Yellow
    docker-compose down
    Write-Host "Containers stopped!" -ForegroundColor Yellow
}

function Restart-WordPress {
    Write-Host "Restarting WordPress containers..." -ForegroundColor Yellow
    docker-compose restart
    Write-Host "Containers restarted!" -ForegroundColor Green
}

function Show-Logs {
    Write-Host "Showing WordPress logs (press Ctrl+C to exit):" -ForegroundColor Cyan
    docker-compose logs -f
}

function Install-WooCommerce {
    Write-Host "Installing WooCommerce..." -ForegroundColor Green
    & .\setup-woocommerce.ps1
}

switch ($Action) {
    "start" { Start-WordPress }
    "stop" { Stop-WordPress }
    "restart" { Restart-WordPress }
    "status" { Show-Status }
    "logs" { Show-Logs }
    "install-woocommerce" { Install-WooCommerce }
}

Write-Host "`nUseful URLs:" -ForegroundColor Cyan
Write-Host "WordPress Site: http://localhost:8080" -ForegroundColor White
Write-Host "WordPress Admin: http://localhost:8080/wp-admin" -ForegroundColor White
Write-Host "phpMyAdmin: http://localhost:8081" -ForegroundColor White
